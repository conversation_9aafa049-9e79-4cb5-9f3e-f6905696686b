import { Injectable, Logger } from '@nestjs/common';
import {
  IOdooAdapter,
  IOdooProtocol,
  IVersionAdapter,
} from '../../../domain/repositories/odoo-adapter.interface';
import {
  OdooConnectionConfig,
  OdooVersionInfo,
  OdooCapabilities,
  SearchReadOptions,
  AuthMethod,
  ProtocolType,
} from '../../../domain/value-objects/odoo-connection-config';
import { PerformanceMonitorService } from './performance-monitor.service';

// Protocol imports
import { XmlRpcProtocol } from '../protocols/xmlrpc/xmlrpc-protocol';
import { JsonRpcProtocol } from '../protocols/jsonrpc/jsonrpc-protocol';
import { RestApiProtocol } from '../protocols/rest/rest-protocol';

// Version adapter imports
import { OdooV18Adapter } from './version-adapters/odoo-v18-adapter';
import { OdooV17Adapter } from './version-adapters/odoo-v17-adapter';
import { OdooV15Adapter } from './version-adapters/odoo-v15-adapter';
import { OdooV13Adapter } from './version-adapters/odoo-v13-adapter';

@Injectable()
export class UniversalOdooAdapter implements IOdooAdapter {
  private readonly logger = new Logger(UniversalOdooAdapter.name);

  private protocol: IOdooProtocol;
  private versionAdapter: IVersionAdapter;
  private versionInfo: OdooVersionInfo | null = null;
  private capabilities: OdooCapabilities | null = null;
  private config: OdooConnectionConfig;

  constructor(
    private readonly xmlRpcProtocol: XmlRpcProtocol,
    private readonly jsonRpcProtocol: JsonRpcProtocol,
    private readonly restApiProtocol: RestApiProtocol,
    private readonly odooV18Adapter: OdooV18Adapter,
    private readonly odooV17Adapter: OdooV17Adapter,
    private readonly odooV15Adapter: OdooV15Adapter,
    private readonly odooV13Adapter: OdooV13Adapter,
    private readonly performanceMonitor: PerformanceMonitorService,
  ) {}

  async connect(): Promise<void> {
    if (!this.config) {
      throw new Error(
        'Connection config not set. Call setConnectionConfig() first.',
      );
    }

    const startTime = Date.now();
    let retryCount = 0;
    const maxRetries = 3;
    const retryDelay = 1000; // 1 second

    while (retryCount < maxRetries) {
      try {
        this.logger.log(`Connection attempt ${retryCount + 1}/${maxRetries} to ${this.config.host}`);

        // 1. Detect version first with timeout
        this.versionInfo = await this.detectVersionWithTimeout();
        this.logger.log(`Detected Odoo version: ${this.versionInfo.series} (${this.versionInfo.edition})`);

        // 2. Create version adapter
        this.versionAdapter = this.createVersionAdapter(this.versionInfo);
        this.capabilities = this.versionAdapter.capabilities;

        // 3. Select best protocol based on capabilities and config
        this.protocol = this.selectOptimalProtocol();
        this.logger.log(`Selected protocol: ${this.protocol.type}`);

        // 4. Connect using selected protocol with timeout
        await this.connectWithTimeout(this.protocol, this.config);

        // 5. Validate connection
        await this.validateConnection();

        const duration = Date.now() - startTime;
        this.logger.log(`Successfully connected to Odoo in ${duration}ms`);
        return;

      } catch (error) {
        retryCount++;
        const isLastAttempt = retryCount >= maxRetries;

        this.logger.warn(
          `Connection attempt ${retryCount} failed: ${error.message}${
            isLastAttempt ? '' : `, retrying in ${retryDelay}ms...`
          }`
        );

        if (isLastAttempt) {
          this.logger.error('All connection attempts failed', error);
          throw new Error(`Connection failed after ${maxRetries} attempts: ${error.message}`);
        }

        // Wait before retry
        await this.sleep(retryDelay * retryCount); // Exponential backoff
      }
    }
  }

  async authenticate(method?: AuthMethod, credentials?: any): Promise<number> {
    const authMethod = method || this.selectBestAuthMethod();
    const creds = credentials || {
      database: this.config.database,
      username: this.config.username,
      password: this.config.password,
    };

    try {
      const uid = await this.protocol.authenticate(authMethod, creds);
      this.logger.log(`Authenticated successfully with method: ${authMethod}`);
      return uid;
    } catch (error) {
      this.logger.error('Authentication failed', error);
      throw error;
    }
  }

  async searchRead<T = any>(
    model: string,
    domain: any[] = [],
    options: SearchReadOptions = {},
  ): Promise<T[]> {
    const startTime = Date.now();
    try {
      // Validate inputs
      this.validateModel(model);
      this.validateDomain(domain);

      // Apply version-specific transformations
      const mappedFields = this.versionAdapter.mapFields(
        model,
        options.fields || [],
      );
      const mappedDomain = this.versionAdapter.mapDomain(domain);
      const mappedMethod = this.versionAdapter.mapMethod(model, 'search_read');

      const result = await this.protocol.execute(
        model,
        mappedMethod,
        [mappedDomain],
        {
          fields: mappedFields.length > 0 ? mappedFields : undefined,
          limit: options.limit,
          offset: options.offset,
          order: options.order,
        },
      );

      const duration = Date.now() - startTime;
      const processedResult = this.versionAdapter.handleResponse(result);

      // Record performance metric
      this.performanceMonitor.recordMetric({
        operation: 'searchRead',
        model,
        duration,
        timestamp: new Date(),
        success: true,
      });

      this.logger.debug(`searchRead for ${model} completed in ${duration}ms (${processedResult?.length || 0} records)`);

      return processedResult;
    } catch (error) {
      const duration = Date.now() - startTime;

      // Record performance metric for failed operation
      this.performanceMonitor.recordMetric({
        operation: 'searchRead',
        model,
        duration,
        timestamp: new Date(),
        success: false,
        error: error.message,
      });

      this.logger.error(
        `searchRead failed for model ${model} after ${duration}ms`,
        {
          model,
          domain,
          options,
          error: error.message,
        }
      );
      throw this.enhanceError(error, 'searchRead', model);
    }
  }

  async create<T = any>(model: string, values: Partial<T>): Promise<number> {
    try {
      const mappedMethod = this.versionAdapter.mapMethod(model, 'create');
      return await this.protocol.execute(model, mappedMethod, [values]);
    } catch (error) {
      this.logger.error(`create failed for model ${model}`, error);
      throw error;
    }
  }

  async write<T = any>(
    model: string,
    ids: number[],
    values: Partial<T>,
  ): Promise<boolean> {
    try {
      const mappedMethod = this.versionAdapter.mapMethod(model, 'write');
      return await this.protocol.execute(model, mappedMethod, [ids, values]);
    } catch (error) {
      this.logger.error(`write failed for model ${model}`, error);
      throw error;
    }
  }

  async unlink(model: string, ids: number[]): Promise<boolean> {
    try {
      const mappedMethod = this.versionAdapter.mapMethod(model, 'unlink');
      return await this.protocol.execute(model, mappedMethod, [ids]);
    } catch (error) {
      this.logger.error(`unlink failed for model ${model}`, error);
      throw error;
    }
  }

  async execute(
    model: string,
    method: string,
    args: any[],
    kwargs?: any,
  ): Promise<any> {
    try {
      const mappedMethod = this.versionAdapter.mapMethod(model, method);
      const result = await this.protocol.execute(
        model,
        mappedMethod,
        args,
        kwargs,
      );
      return this.versionAdapter.handleResponse(result);
    } catch (error) {
      this.logger.error(`execute failed for ${model}.${method}`, error);
      throw error;
    }
  }

  getVersionInfo(): OdooVersionInfo | null {
    return this.versionInfo;
  }

  getCapabilities(): OdooCapabilities | null {
    return this.capabilities;
  }

  async disconnect(): Promise<void> {
    if (this.protocol) {
      await this.protocol.disconnect();
    }
    this.logger.log('Disconnected from Odoo');
  }

  setConnectionConfig(config: OdooConnectionConfig): void {
    this.config = config;
  }

  // ============= PRIVATE METHODS =============

  private async detectVersion(): Promise<OdooVersionInfo> {
    // Try multiple detection methods
    try {
      // Method 1: XML-RPC common service
      return await this.detectVersionViaXmlRpc();
    } catch (error) {
      try {
        // Method 2: JSON-RPC web client info
        return await this.detectVersionViaJsonRpc();
      } catch (error) {
        // Method 3: HTTP endpoint
        return await this.detectVersionViaHttp();
      }
    }
  }

  private async detectVersionViaXmlRpc(): Promise<OdooVersionInfo> {
    const xmlrpc = require('xmlrpc');
    const port =
      this.config.port || (this.config.protocol === 'https' ? 443 : 80);

    // Clean host - remove protocol if present
    const cleanHost = this.config.host.replace(/^https?:\/\//, '');

    const client = xmlrpc.createClient({
      host: cleanHost,
      port,
      path: '/xmlrpc/2/common',
    });

    return new Promise((resolve, reject) => {
      client.methodCall('version', [], (error: any, value: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(this.parseVersionInfo(value));
        }
      });
    });
  }

  private async detectVersionViaJsonRpc(): Promise<OdooVersionInfo> {
    // Clean host - remove protocol if present
    const cleanHost = this.config.host.replace(/^https?:\/\//, '');
    const baseUrl = `${this.config.protocol}://${cleanHost}:${this.config.port || 80}`;

    const response = await fetch(`${baseUrl}/web/webclient/version_info`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'call',
        params: {},
      }),
    });

    const result = await response.json();
    return this.parseVersionInfo(result.result);
  }

  private async detectVersionViaHttp(): Promise<OdooVersionInfo> {
    // Clean host - remove protocol if present
    const cleanHost = this.config.host.replace(/^https?:\/\//, '');
    const baseUrl = `${this.config.protocol}://${cleanHost}:${this.config.port || 80}`;

    const response = await fetch(`${baseUrl}/web/webclient/version_info`);
    const result = await response.json();
    return this.parseVersionInfo(result);
  }

  private parseVersionInfo(versionData: any): OdooVersionInfo {
    const version = versionData.server_version || versionData.version;
    // Clean version string and split
    const cleanVersion = version.replace(/[^0-9.]/g, ''); // Remove non-numeric chars except dots
    const versionParts = cleanVersion.split('.');

    const major = parseInt(versionParts[0]) || 0;
    const minor = parseInt(versionParts[1]) || 0;
    const patch = parseInt(versionParts[2]) || 0;

    return {
      major,
      minor,
      patch,
      series: `${major}.${minor}`,
      edition: this.detectEdition(versionData),
      serverVersion: version,
      protocolVersion: versionData.protocol_version || 1,
    };
  }

  private detectEdition(versionData: any): 'community' | 'enterprise' {
    // Try to detect if it's enterprise edition
    const version = versionData.server_version || versionData.version || '';
    return version.includes('e') || versionData.enterprise
      ? 'enterprise'
      : 'community';
  }

  private createVersionAdapter(version: OdooVersionInfo): IVersionAdapter {
    if (version.major >= 18) return this.odooV18Adapter;
    if (version.major >= 17) return this.odooV17Adapter;
    if (version.major >= 15) return this.odooV15Adapter;
    if (version.major >= 13) return this.odooV13Adapter;

    throw new Error(`Unsupported Odoo version: ${version.series}`);
  }

  private selectOptimalProtocol(): IOdooProtocol {
    // Consider user preference if specified in config
    const preferredProtocol = (this.config as any).preferredProtocol;

    if (preferredProtocol === 'rest' && this.capabilities?.hasRestApi) {
      this.logger.debug('Using REST API as preferred protocol');
      return this.restApiProtocol;
    }

    if (preferredProtocol === 'jsonrpc' && this.capabilities?.hasJsonRpc) {
      this.logger.debug('Using JSON-RPC as preferred protocol');
      return this.jsonRpcProtocol;
    }

    // Auto-select best protocol based on capabilities and version
    if (this.capabilities?.hasRestApi && this.versionInfo?.major >= 17) {
      this.logger.debug('Auto-selected REST API for modern Odoo version');
      return this.restApiProtocol;
    }

    // Prefer JSON-RPC if available (faster than XML-RPC)
    if (this.capabilities?.hasJsonRpc) {
      this.logger.debug('Auto-selected JSON-RPC');
      return this.jsonRpcProtocol;
    }

    // Fallback to XML-RPC (universal support)
    this.logger.debug('Fallback to XML-RPC');
    return this.xmlRpcProtocol;
  }

  private selectBestAuthMethod(): AuthMethod {
    const supported = this.capabilities?.supportedAuthMethods || [];

    // Priority order: API Key > OAuth2 > Token > Password
    if (supported.includes(AuthMethod.API_KEY)) return AuthMethod.API_KEY;
    if (supported.includes(AuthMethod.OAUTH2)) return AuthMethod.OAUTH2;
    if (supported.includes(AuthMethod.TOKEN)) return AuthMethod.TOKEN;

    return AuthMethod.PASSWORD; // Fallback
  }

  private async detectVersionWithTimeout(): Promise<OdooVersionInfo> {
    const timeout = 10000; // 10 seconds
    return Promise.race([
      this.detectVersion(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Version detection timeout')), timeout)
      )
    ]);
  }

  private async connectWithTimeout(protocol: IOdooProtocol, config: OdooConnectionConfig): Promise<void> {
    const timeout = 15000; // 15 seconds
    return Promise.race([
      protocol.connect(config),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Protocol connection timeout')), timeout)
      )
    ]);
  }

  private async validateConnection(): Promise<void> {
    try {
      // Test connection with a simple query
      await this.searchRead('res.users', [['id', '=', 1]], { limit: 1 });
      this.logger.debug('Connection validation successful');
    } catch (error) {
      throw new Error(`Connection validation failed: ${error.message}`);
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private validateModel(model: string): void {
    if (!model || typeof model !== 'string') {
      throw new Error('Model name is required and must be a string');
    }
    if (!/^[a-z][a-z0-9_.]*$/.test(model)) {
      throw new Error(`Invalid model name format: ${model}`);
    }
  }

  private validateDomain(domain: any[]): void {
    if (!Array.isArray(domain)) {
      throw new Error('Domain must be an array');
    }
    // Additional domain validation could be added here
  }

  private enhanceError(error: any, operation: string, model?: string): Error {
    const enhancedMessage = `${operation} operation failed${model ? ` for model ${model}` : ''}`;

    // Check for common Odoo errors and provide better messages
    if (error.message?.includes('AccessError')) {
      return new Error(`${enhancedMessage}: Access denied. Check user permissions for ${model}`);
    }

    if (error.message?.includes('ValidationError')) {
      return new Error(`${enhancedMessage}: Data validation failed. ${error.message}`);
    }

    if (error.message?.includes('MissingError')) {
      return new Error(`${enhancedMessage}: Record not found or has been deleted`);
    }

    if (error.message?.includes('UserError')) {
      return new Error(`${enhancedMessage}: ${error.message}`);
    }

    // Network/connection errors
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return new Error(`${enhancedMessage}: Cannot connect to Odoo server. Check host and port configuration`);
    }

    if (error.code === 'ETIMEDOUT') {
      return new Error(`${enhancedMessage}: Connection timeout. Server may be overloaded`);
    }

    // Return enhanced error with original message
    const enhancedError = new Error(`${enhancedMessage}: ${error.message}`);
    enhancedError.stack = error.stack;
    return enhancedError;
  }
}
